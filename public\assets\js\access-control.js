/**
 * Access Control for Timer and Chat Features
 * Simple implementation without API calls to avoid rate limiting
 */

// Store original functions
let originalOpenTimerSection = null;
let originalToggleChatIframe = null;

// Simple access control - always allow for now to fix the chat issue
// You can implement server-side checks in the actual endpoints instead
const ACCESS_CONTROL = {
    messages: {
        timer: 'Please purchase a subscription to access the timer feature',
        chatSubscription: 'Please purchase a subscription to access the chat feature',
        chatWorkspace: 'Please create a workspace first to access the chat feature'
    },

    // For now, always return true to allow access
    // The actual access control should be implemented server-side
    checkSubscription() {
        return true; // Always allow to fix the current issues
    },

    checkWorkspace() {
        return true; // Always allow to fix the current issues
    },

    // Show error message
    showError(message) {
        if (typeof toastr !== 'undefined') {
            toastr.error(message, 'Access Denied');
        } else {
            alert(message);
        }
    }
};

// Override timer function - for now just call original to fix issues
function open_timer_section() {
    // Call original function directly to avoid issues
    if (originalOpenTimerSection) {
        originalOpenTimerSection();
    } else {
        // Fallback to original logic
        if (typeof is_timer_stopped !== 'undefined' && is_timer_stopped) {
            $("#pause").attr("disabled", true);
            $("#end").attr("disabled", true);
        }

        if (typeof $ !== 'undefined') {
            $("#time_tracker_message").val(localStorage.getItem("msg"));

            // Check if there's a saved project
            if (localStorage.getItem("project_id")) {
                var projectId = localStorage.getItem("project_id");
                var taskId = localStorage.getItem("task_id");

                $("#time_tracker_project").val(projectId).trigger('change');

                if (taskId) {
                    setTimeout(function() {
                        $("#time_tracker_task").val(taskId);
                    }, 500);
                }
            }
        }
    }
}

// Override chat function - for now just call original to fix issues
function toggleChatIframe() {
    // Call original function directly to avoid issues
    if (originalToggleChatIframe) {
        originalToggleChatIframe();
    } else {
        // Fallback to original logic
        const chatContainer = document.getElementById('chatIframeContainer');
        if (chatContainer) {
            if (chatContainer.style.display === 'none' || chatContainer.style.display === '') {
                chatContainer.style.display = 'block';
            } else {
                chatContainer.style.display = 'none';
            }
        }
    }
}

// Initialize access control when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Store original functions if they exist
    if (typeof window.open_timer_section === 'function') {
        originalOpenTimerSection = window.open_timer_section;
    }

    if (typeof window.toggleChatIframe === 'function') {
        originalToggleChatIframe = window.toggleChatIframe;
    }

    console.log('Access control initialized - functions working normally');
});
