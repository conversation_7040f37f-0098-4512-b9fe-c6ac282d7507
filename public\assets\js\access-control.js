/**
 * Access Control for Timer and Chat Features
 * This script implements subscription and workspace-based access control
 */

// Store original functions
let originalOpenTimerSection = null;
let originalToggleChatIframe = null;

// Access control configuration
const ACCESS_CONTROL = {
    messages: {
        timer: 'Please purchase a subscription to access the timer feature',
        chatSubscription: 'Please purchase a subscription to access the chat feature',
        chatWorkspace: 'Please create a workspace first to access the chat feature'
    },
    
    // Check if user has active subscription
    async checkSubscription() {
        try {
            const response = await fetch('/api/check-subscription', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                return data.hasSubscription || false;
            }
        } catch (error) {
            console.log('Subscription check failed, allowing access');
        }
        return true; // Default to allow access if check fails
    },
    
    // Check if user has workspace
    async checkWorkspace() {
        try {
            const response = await fetch('/api/check-workspace', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                return data.hasWorkspace || false;
            }
        } catch (error) {
            console.log('Workspace check failed, allowing access');
        }
        return true; // Default to allow access if check fails
    },
    
    // Show error message
    showError(message) {
        if (typeof toastr !== 'undefined') {
            toastr.error(message, 'Access Denied');
        } else {
            alert(message);
        }
    }
};

// Override timer function with access control
function open_timer_section() {
    ACCESS_CONTROL.checkSubscription().then(hasSubscription => {
        if (!hasSubscription) {
            ACCESS_CONTROL.showError(ACCESS_CONTROL.messages.timer);
            return;
        }
        
        // If subscription check passes, call original function
        if (originalOpenTimerSection) {
            originalOpenTimerSection();
        } else {
            // Fallback to original logic
            const isTimerRunning = !is_timer_stopped || 
                                  (localStorage.getItem("Seconds") > "00" && 
                                   localStorage.getItem("Pause") !== "0");

            if (is_timer_stopped) {
                $("#pause").attr("disabled", true);
                $("#end").attr("disabled", true);
            }

            $("#time_tracker_message").val(localStorage.getItem("msg"));

            // Check if there's a saved project
            if (localStorage.getItem("project_id")) {
                var projectId = localStorage.getItem("project_id");
                var taskId = localStorage.getItem("task_id");
                var urlPrefix = window.location.pathname.split("/")[1];
                
                // Continue with original timer logic...
                $("#time_tracker_project").val(projectId).trigger('change');
                
                if (taskId) {
                    setTimeout(function() {
                        $("#time_tracker_task").val(taskId);
                    }, 500);
                }
            }
        }
    });
}

// Override chat function with access control
function toggleChatIframe() {
    ACCESS_CONTROL.checkSubscription().then(hasSubscription => {
        if (!hasSubscription) {
            ACCESS_CONTROL.showError(ACCESS_CONTROL.messages.chatSubscription);
            return;
        }
        
        ACCESS_CONTROL.checkWorkspace().then(hasWorkspace => {
            if (!hasWorkspace) {
                ACCESS_CONTROL.showError(ACCESS_CONTROL.messages.chatWorkspace);
                return;
            }
            
            // If all checks pass, call original function
            if (originalToggleChatIframe) {
                originalToggleChatIframe();
            } else {
                // Fallback to original logic
                const chatContainer = document.getElementById('chatIframeContainer');
                if (chatContainer) {
                    if (chatContainer.style.display === 'none' || chatContainer.style.display === '') {
                        chatContainer.style.display = 'block';
                    } else {
                        chatContainer.style.display = 'none';
                    }
                }
            }
        });
    });
}

// Initialize access control when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Store original functions if they exist
    if (typeof window.open_timer_section === 'function') {
        originalOpenTimerSection = window.open_timer_section;
    }
    
    if (typeof window.toggleChatIframe === 'function') {
        originalToggleChatIframe = window.toggleChatIframe;
    }
    
    console.log('Access control initialized');
});
