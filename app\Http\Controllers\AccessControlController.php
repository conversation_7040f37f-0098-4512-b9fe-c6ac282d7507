<?php

namespace App\Http\Controllers;

use App\Models\Admin;
use App\Models\Subscription;
use App\Models\Workspace;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AccessControlController extends Controller
{
    /**
     * Check if the current user has an active subscription
     */
    public function checkSubscription(): JsonResponse
    {
        $user = getAuthenticatedUser();
        
        if (!$user) {
            return response()->json(['hasSubscription' => false]);
        }

        // Skip subscription check for superadmin and manager roles
        if ($user->hasRole('superadmin') || $user->hasRole('manager')) {
            return response()->json(['hasSubscription' => true]);
        }

        $hasSubscription = false;
        
        if ($user->hasRole('admin')) {
            $subscription = Subscription::where(['user_id' => $user->id, 'status' => 'active'])->first();
            $hasSubscription = $subscription !== null;
        } else {
            $adminId = getAdminIdByUserRole();
            if ($adminId) {
                $admin = Admin::find($adminId);
                if ($admin) {
                    $subscription = Subscription::where(['user_id' => $admin->user_id, 'status' => 'active'])->first();
                    $hasSubscription = $subscription !== null;
                }
            }
        }

        return response()->json(['hasSubscription' => $hasSubscription]);
    }

    /**
     * Check if the current user has at least one workspace
     */
    public function checkWorkspace(): JsonResponse
    {
        $user = getAuthenticatedUser();
        
        if (!$user) {
            return response()->json(['hasWorkspace' => false]);
        }

        // Skip workspace check for superadmin and manager roles
        if ($user->hasRole('superadmin') || $user->hasRole('manager')) {
            return response()->json(['hasWorkspace' => true]);
        }

        $hasWorkspace = false;
        
        // Check if user has workspace in session or header
        $workspaceId = session('workspace_id') ?: request()->header('workspace_id');
        
        if ($workspaceId && $workspaceId != 0) {
            $hasWorkspace = true;
        } else {
            // Fallback: Check if user is associated with any workspace
            if (isAdminOrHasAllDataAccess()) {
                $adminId = getAdminIdByUserRole();
                $workspaceCount = Workspace::where('admin_id', $adminId)->count();
                $hasWorkspace = $workspaceCount > 0;
            } else {
                $hasWorkspace = $user->workspaces()->count() > 0;
            }
        }

        return response()->json(['hasWorkspace' => $hasWorkspace]);
    }
}
