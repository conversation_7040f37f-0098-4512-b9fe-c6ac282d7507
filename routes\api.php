<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SubscriptionPlan;
use App\Http\Controllers\AccessControlController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Access control routes
Route::middleware(['web', 'auth'])->group(function () {
    Route::get('/check-subscription', [AccessControlController::class, 'checkSubscription']);
    Route::get('/check-workspace', [AccessControlController::class, 'checkWorkspace']);
});
